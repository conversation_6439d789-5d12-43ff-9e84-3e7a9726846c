import sensor, image, time

# 初始化摄像头
sensor.reset()
sensor.set_pixformat(sensor.GRAYSCALE)  # 改为灰度图像，节省内存
sensor.set_framesize(sensor.QQVGA)  # 改为更小的分辨率160x120，节省内存
sensor.skip_frames(time=2000)

MIN_AREA = 100  # 降低最小面积阈值
MAX_AREA = 80000  # 最大面积阈值

# 主循环

while True:

    try:


        img = sensor.snapshot()



        if img is None:

            continue

        rect_center = (-1, -1)

        # 使用多种方法进行矩形检测，提高识别率

        try:
            # 先进行图像预处理，提高检测效果
            img_processed = img.copy()
            img_processed.binary([(0, 80)])  # 二值化处理
            img_processed.erode(1)  # 腐蚀去噪
            img_processed.dilate(2)  # 膨胀填充

            # 方法1: 检测黑色区域 (矩形通常是黑色)
            black_blobs = img_processed.find_blobs([(0, 50)], pixels_threshold=50, area_threshold=MIN_AREA, merge=True)

            # 方法2: 检测白色区域 (有时矩形是白色)
            white_blobs = img_processed.find_blobs([(200, 255)], pixels_threshold=50, area_threshold=MIN_AREA, merge=True)

            # 合并所有候选
            all_blobs = black_blobs + white_blobs
            print(f"检测到 {len(all_blobs)} 个候选区域")

            # 找到最符合矩形特征的blob
            best_blob = None
            best_score = 0

            for i, blob in enumerate(all_blobs):
                area = blob.area()

                # 面积检查
                if area < MIN_AREA or area > MAX_AREA:
                    continue

                # 边界检查 (调整为160x120分辨率)
                if blob.x() < 2 or blob.y() < 2 or \
                   blob.x() + blob.w() > 157 or blob.y() + blob.h() > 117:
                    continue

                # 多重评分标准
                rect_area = blob.w() * blob.h()
                if rect_area > 0:
                    # 1. 矩形度 (面积与外接矩形面积的比值)
                    rectangularity = area / rect_area

                    # 2. 长宽比合理性 (避免过于细长的形状)
                    aspect_ratio = max(blob.w(), blob.h()) / min(blob.w(), blob.h())
                    aspect_score = 1.0 / (1.0 + abs(aspect_ratio - 1.5))  # 偏好1:1.5的长宽比

                    # 3. 密实度 (blob的紧凑程度)
                    perimeter = 2 * (blob.w() + blob.h())
                    if perimeter > 0:
                        compactness = (4 * 3.14159 * area) / (perimeter * perimeter)
                    else:
                        compactness = 0

                    # 4. 大小偏好 (偏好中等大小)
                    size_score = 1.0 / (1.0 + abs(area - 1000))

                    # 综合评分
                    score = (rectangularity * 0.4 +
                            aspect_score * 0.3 +
                            compactness * 0.2 +
                            size_score * 0.1)

                    print(f"候选 {i}: 面积={area}, 矩形度={rectangularity:.3f}, 长宽比={aspect_ratio:.2f}, 评分={score:.3f}")

                    if score > best_score:
                        best_score = score
                        best_blob = blob

            # 绘制最佳blob和中心点
            if best_blob and best_score > 0.1:  # 设置最低评分阈值
                # 绘制矩形框
                img.draw_rectangle(best_blob.rect(), color=255, thickness=2)
                print(f"绘制矩形: ({best_blob.x()}, {best_blob.y()}, {best_blob.w()}, {best_blob.h()}), 评分={best_score:.3f}")

                # 计算中心点
                center_x = best_blob.cx()
                center_y = best_blob.cy()
                rect_center = (center_x, center_y)

                # 绘制中心点
                img.draw_circle(center_x, center_y, 3, color=255, fill=True)
            else:
                print("没有找到符合条件的矩形")

        except Exception as e:

            print("矩形检测异常:", e)
 
 
 
        # 打印矩形中心点坐标

        try:
            if rect_center[0] >= 0 and rect_center[1] >= 0:
                print(f"矩形中心点: ({rect_center[0]}, {rect_center[1]})")
            else:
                print("矩形中心点: (0, 0)")

        except Exception as e:

            print("坐标处理异常:", e)
 
 
 
        # OpenMV会自动在IDE中显示图像，无需额外显示代码
        pass
 
 
    except Exception as e:
 
        print("主循环异常:", e)
