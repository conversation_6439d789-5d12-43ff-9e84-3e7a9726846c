import sensor, image, time

# 初始化摄像头
sensor.reset()
sensor.set_pixformat(sensor.GRAYSCALE)  # 改为灰度图像，节省内存
sensor.set_framesize(sensor.QQVGA)  # 改为更小的分辨率160x120，节省内存
sensor.skip_frames(time=2000)

MIN_AREA = 100  # 降低最小面积阈值
MAX_AREA = 80000  # 最大面积阈值

# 主循环

while True:

    try:


        img = sensor.snapshot()



        if img is None:

            continue

        rect_center = (-1, -1)

        # 简化矩形检测，直接在原图上操作

        try:
            # 先在原图上绘制一个测试标记，确保绘制功能正常
            img.draw_circle(10, 10, 5, color=255, fill=True)  # 左上角测试点

            # 在副本上进行二值化处理，尝试多个阈值
            img_processed = img.copy()

            # 尝试检测暗色区域（矩形通常比背景暗）
            blobs1 = img.find_blobs([(0, 80)], pixels_threshold=50, area_threshold=MIN_AREA, merge=True)
            # 尝试检测亮色区域（有时矩形比背景亮）
            blobs2 = img.find_blobs([(100, 255)], pixels_threshold=50, area_threshold=MIN_AREA, merge=True)

            # 合并两种检测结果
            blobs = blobs1 + blobs2
            print(f"检测到 {len(blobs)} 个区域")

            # 按面积排序，优先处理较小的（类似原始算法）
            blobs_sorted = sorted(blobs, key=lambda b: b.area())

            # 找到最小的符合条件的矩形
            min_rect = None
            min_area = float('inf')

            for i, blob in enumerate(blobs_sorted):
                area = blob.area()

                print(f"区域 {i}: 位置({blob.x()}, {blob.y()}), 大小({blob.w()}x{blob.h()}), 面积={area}")

                # 面积检查
                if area < MIN_AREA:
                    print(f"区域 {i} 面积太小")
                    continue
                if area > MAX_AREA:
                    print(f"区域 {i} 面积太大，停止检查")
                    break  # 由于已排序，后面的都会更大

                # 边界检查 - 只排除完全贴边的大区域
                margin = 5  # 边界容忍度
                if (blob.x() <= margin and blob.w() >= 150) or \
                   (blob.y() <= margin and blob.h() >= 110) or \
                   (blob.x() + blob.w() >= 155 and blob.w() >= 150) or \
                   (blob.y() + blob.h() >= 115 and blob.h() >= 110):
                    print(f"区域 {i} 是边界大区域，跳过")
                    continue

                # 简单的矩形度检查（类似原始的is_rectangle函数）
                rect_area = blob.w() * blob.h()
                if rect_area > 0:
                    rectangularity = area / rect_area
                    # 只要矩形度合理就接受（不要太严格）
                    if rectangularity > 0.3:  # 比较宽松的阈值
                        if area < min_area:
                            min_area = area
                            min_rect = blob
                            print(f"找到更小的矩形: 面积={area}, 矩形度={rectangularity:.3f}")
                    else:
                        print(f"区域 {i} 矩形度不够: {rectangularity:.3f}")

            # 直接在原图上绘制最小矩形和中心点
            if min_rect:
                print(f"开始绘制矩形: ({min_rect.x()}, {min_rect.y()}, {min_rect.w()}, {min_rect.h()})")

                # 绘制矩形框
                x, y, w, h = min_rect.x(), min_rect.y(), min_rect.w(), min_rect.h()

                # 手动绘制矩形的四条边，确保可见
                img.draw_line(x, y, x+w, y, color=255, thickness=3)      # 上边
                img.draw_line(x+w, y, x+w, y+h, color=255, thickness=3)  # 右边
                img.draw_line(x+w, y+h, x, y+h, color=255, thickness=3)  # 下边
                img.draw_line(x, y+h, x, y, color=255, thickness=3)      # 左边

                # 绘制四个角点
                img.draw_circle(x, y, 4, color=255, fill=True)      # 左上角
                img.draw_circle(x+w, y, 4, color=255, fill=True)    # 右上角
                img.draw_circle(x, y+h, 4, color=255, fill=True)    # 左下角
                img.draw_circle(x+w, y+h, 4, color=255, fill=True)  # 右下角

                # 计算中心点
                center_x = min_rect.cx()
                center_y = min_rect.cy()
                rect_center = (center_x, center_y)

                # 绘制中心点
                img.draw_circle(center_x, center_y, 6, color=255, fill=True)
                # 绘制十字标记
                img.draw_line(center_x-10, center_y, center_x+10, center_y, color=255, thickness=3)
                img.draw_line(center_x, center_y-10, center_x, center_y+10, color=255, thickness=3)

                print(f"绘制完成！中心点: ({center_x}, {center_y})")

            else:
                print("没有找到符合条件的矩形")

        except Exception as e:

            print("矩形检测异常:", e)
 
 
 
        # 打印矩形中心点坐标

        try:
            if rect_center[0] >= 0 and rect_center[1] >= 0:
                print(f"矩形中心点: ({rect_center[0]}, {rect_center[1]})")
            else:
                print("矩形中心点: (0, 0)")

        except Exception as e:

            print("坐标处理异常:", e)
 
 
 
        # OpenMV会自动在IDE中显示图像，无需额外显示代码
        pass
 
 
    except Exception as e:
 
        print("主循环异常:", e)
