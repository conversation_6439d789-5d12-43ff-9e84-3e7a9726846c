import sensor, image, time

# 初始化摄像头
sensor.reset()
sensor.set_pixformat(sensor.GRAYSCALE)  # 改为灰度图像，节省内存
sensor.set_framesize(sensor.QQVGA)  # 改为更小的分辨率160x120，节省内存
sensor.skip_frames(time=2000)

MIN_AREA = 100  # 降低最小面积阈值
MAX_AREA = 80000  # 最大面积阈值

# 主循环

while True:

    try:


        img = sensor.snapshot()



        if img is None:

            continue

        rect_center = (-1, -1)

        # 使用更节省内存的方法进行矩形检测

        try:
            # 使用blob检测替代find_rects，更节省内存
            blobs = img.find_blobs([(0, 100)], pixels_threshold=200, area_threshold=MIN_AREA, merge=True)
            print(f"检测到 {len(blobs)} 个blob")

            # 找到最符合矩形特征的blob
            best_blob = None
            best_score = 0

            for i, blob in enumerate(blobs):
                area = blob.area()

                # 面积检查
                if area < MIN_AREA or area > MAX_AREA:
                    continue

                # 边界检查 (调整为160x120分辨率)
                if blob.x() < 1 or blob.y() < 1 or \
                   blob.x() + blob.w() > 158 or blob.y() + blob.h() > 118:
                    continue

                # 计算矩形度 (面积与外接矩形面积的比值)
                rect_area = blob.w() * blob.h()
                if rect_area > 0:
                    rectangularity = area / rect_area
                    # 矩形度越接近1越好，同时考虑面积大小
                    score = rectangularity * (1.0 / (1.0 + abs(area - 2000)))  # 偏好中等大小的矩形

                    if score > best_score:
                        best_score = score
                        best_blob = blob

            # 绘制最佳blob和中心点
            if best_blob:
                # 绘制矩形框
                img.draw_rectangle(best_blob.rect(), color=255, thickness=2)  # 灰度图用255表示白色
                print(f"绘制矩形: ({best_blob.x()}, {best_blob.y()}, {best_blob.w()}, {best_blob.h()})")

                # 计算中心点
                center_x = best_blob.cx()
                center_y = best_blob.cy()
                rect_center = (center_x, center_y)

                # 绘制中心点
                img.draw_circle(center_x, center_y, 2, color=255, fill=True)
            else:
                print("没有找到符合条件的矩形")

        except Exception as e:

            print("矩形检测异常:", e)
 
 
 
        # 打印矩形中心点坐标

        try:
            if rect_center[0] >= 0 and rect_center[1] >= 0:
                print(f"矩形中心点: ({rect_center[0]}, {rect_center[1]})")
            else:
                print("矩形中心点: (0, 0)")

        except Exception as e:

            print("坐标处理异常:", e)
 
 
 
        # OpenMV会自动在IDE中显示图像，无需额外显示代码
        pass
 
 
    except Exception as e:
 
        print("主循环异常:", e)
