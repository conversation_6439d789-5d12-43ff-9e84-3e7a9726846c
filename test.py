import sensor, image, time

# 初始化摄像头
sensor.reset()
sensor.set_pixformat(sensor.RGB565)
sensor.set_framesize(sensor.QVGA)  # 320x240
sensor.skip_frames(time=2000)

MIN_AREA = 100  # 降低最小面积阈值
MAX_AREA = 80000  # 最大面积阈值

# 主循环

while True:

    try:


        img = sensor.snapshot()



        if img is None:

            continue

        rect_center = (-1, -1)

        # 使用OpenMV内置的矩形检测

        try:
            rects = img.find_rects(threshold=500)  # 降低阈值
            print(f"检测到 {len(rects)} 个矩形")  # 调试信息

            # 找到面积最小的符合条件的矩形
            min_rect = None
            min_area = float('inf')

            for i, rect in enumerate(rects):
                area = rect.w() * rect.h()
                print(f"矩形 {i}: 位置({rect.x()}, {rect.y()}), 大小({rect.w()}x{rect.h()}), 面积={area}")

                # 面积检查
                if area < MIN_AREA or area > MAX_AREA:
                    print(f"矩形 {i} 面积不符合要求 (范围: {MIN_AREA}-{MAX_AREA})")
                    continue

                # 边界检查
                if rect.x() < 1 or rect.y() < 1 or \
                   rect.x() + rect.w() > 318 or rect.y() + rect.h() > 238:
                    print(f"矩形 {i} 超出边界")
                    continue

                if area < min_area:
                    min_area = area
                    min_rect = rect
                    print(f"找到更小的矩形: 面积={area}")

            # 绘制矩形和中心点
            if min_rect:
                # 绘制矩形
                img.draw_rectangle(min_rect, color=(0, 255, 0), thickness=2)
                print(f"绘制矩形: ({min_rect.x()}, {min_rect.y()}, {min_rect.w()}, {min_rect.h()})")

                # 计算中心点
                center_x = min_rect.x() + min_rect.w() // 2
                center_y = min_rect.y() + min_rect.h() // 2
                rect_center = (center_x, center_y)

                # 绘制中心点
                img.draw_circle(center_x, center_y, 2, color=(255, 255, 0), fill=True)
            else:
                print("没有找到符合条件的矩形")

        except Exception as e:

            print("矩形检测异常:", e)
 
 
 
        # 打印矩形中心点坐标

        try:
            if rect_center[0] >= 0 and rect_center[1] >= 0:
                print(f"矩形中心点: ({rect_center[0]}, {rect_center[1]})")
            else:
                print("矩形中心点: (0, 0)")

        except Exception as e:

            print("坐标处理异常:", e)
 
 
 
        # OpenMV会自动在IDE中显示图像，无需额外显示代码
        pass
 
 
    except Exception as e:
 
        print("主循环异常:", e)
